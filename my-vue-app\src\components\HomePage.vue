<template>
  <div class="home-page">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="signal-bars">
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
        </div>
      </div>
      <div class="status-right">
        <div class="battery-indicator">
          <div class="battery-level"></div>
        </div>
      </div>
    </div>

    <!-- 用户信息区域 -->
    <div class="user-header">
      <div class="user-avatar">
        <img :src="avatarRachel" alt="Rachel" />
      </div>
      <div class="user-name">Rachel</div>
    </div>

    <!-- 孩子选择区域 -->
    <div class="children-section">
      <div class="child-avatar" 
           v-for="child in children" 
           :key="child.id"
           :class="{ active: child.id === selectedChildId }"
           @click="selectChild(child.id)">
        <img :src="child.avatar" :alt="child.name" />
        <span class="child-name">{{ child.name }}</span>
      </div>
      
      <!-- 添加孩子按钮 -->
      <div class="add-child-btn" @click="addChild">
        <div class="add-icon-container">
          <img :src="addIcon" alt="添加" />
        </div>
        <span class="child-name">孩子</span>
      </div>
    </div>

    <!-- 当前孩子信息卡片 -->
    <div class="child-info-card" v-if="selectedChild">
      <div class="child-avatar-large">
        <img :src="selectedChild.avatar" :alt="selectedChild.name" />
      </div>
      <div class="child-details">
        <div class="child-name-large">{{ selectedChild.name }}</div>
        <div class="child-meta">
          <span class="birth-date">{{ selectedChild.birthDate }}</span>
          <span class="age">{{ selectedChild.age }}岁</span>
        </div>
        <div class="school-info">
          <div class="school-name">{{ selectedChild.school }}</div>
          <div class="class-info">
            <span class="class-name">{{ selectedChild.className }}</span>
            <span class="student-number">{{ selectedChild.studentNumber }}</span>
          </div>
        </div>
      </div>
      <div class="wechat-icon">
        <img :src="wechatSmall" alt="微信" />
      </div>
    </div>

    <!-- 学年信息 -->
    <div class="academic-year">2025～2026学年 上学期</div>

    <!-- 作文列表 -->
    <div class="essay-list">
      <div class="essay-item" v-for="essay in essays" :key="essay.id">
        <div class="essay-content">
          <div class="essay-tags">
            <span class="unit-tag">{{ essay.unit }}</span>
            <span class="type-tag">{{ essay.type }}</span>
            <span class="category-tag">{{ essay.category }}</span>
          </div>
          <div class="essay-title">{{ essay.title }}</div>
          <div class="essay-time">{{ essay.time }}</div>
        </div>
        
        <div class="essay-actions">
          <div class="essay-score" v-if="essay.score">
            <span class="score-number">{{ essay.score }}</span>
            <span class="score-unit">分</span>
          </div>
          <button class="upload-btn" v-else @click="uploadEssay(essay.id)">
            上传作文
          </button>
          <div class="arrow-icon">
            <img :src="arrowRight" alt="查看" />
          </div>
        </div>
      </div>
    </div>

    <!-- 底线 -->
    <div class="bottom-line">------我是有底线的------</div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
      <div class="tab-item" :class="{ active: currentTab === 'home' }" @click="switchTab('home')">
        <img :src="homeIcon" alt="首页" class="tab-icon" />
        <span class="tab-text">首页</span>
        <div class="tab-indicator" v-if="currentTab === 'home'"></div>
      </div>

      <div class="tab-item" :class="{ active: currentTab === 'data' }" @click="switchTab('data')">
        <img :src="assessmentIcon" alt="成长数据" class="tab-icon" />
        <span class="tab-text">成长数据</span>
      </div>

      <div class="tab-item" :class="{ active: currentTab === 'history' }" @click="switchTab('history')">
        <img :src="folderIcon" alt="过往作文" class="tab-icon" />
        <span class="tab-text">过往作文</span>
      </div>

      <div class="tab-item" :class="{ active: currentTab === 'profile' }" @click="switchTab('profile')">
        <img :src="userIcon" alt="个人中心" class="tab-icon" />
        <span class="tab-text">个人中心</span>
      </div>
    </div>
  </div>
</template>

<script>
import avatarRachel from '../assets/images/avatar-rachel.png'
import avatarChild1 from '../assets/images/avatar-child1.png'
import avatarChild2 from '../assets/images/avatar-child2.png'
import avatarChild3 from '../assets/images/avatar-child3.png'
import addIcon from '../assets/images/add-icon.svg'
import wechatSmall from '../assets/images/wechat-small.svg'
import arrowRight from '../assets/images/arrow-right.svg'
import homeIcon from '../assets/images/home-icon.svg'
import assessmentIcon from '../assets/images/assessment-icon.svg'
import folderIcon from '../assets/images/folder-icon.svg'
import userIcon from '../assets/images/user-icon.svg'

export default {
  name: 'HomePage',
  data() {
    return {
      avatarRachel,
      addIcon,
      wechatSmall,
      arrowRight,
      homeIcon,
      assessmentIcon,
      folderIcon,
      userIcon,
      currentTab: 'home',
      selectedChildId: 1,
      children: [
        {
          id: 1,
          name: '孩子1',
          avatar: avatarChild1,
          birthDate: '2020.06.03',
          age: 6,
          school: '江头中心小学',
          className: '一年一班',
          studentNumber: '09号'
        },
        {
          id: 2,
          name: '孩子2',
          avatar: avatarChild2,
          birthDate: '2018.03.15',
          age: 8,
          school: '江头中心小学',
          className: '二年三班',
          studentNumber: '15号'
        },
        {
          id: 3,
          name: '孩子3',
          avatar: avatarChild3,
          birthDate: '2016.09.20',
          age: 10,
          school: '江头中心小学',
          className: '四年二班',
          studentNumber: '22号'
        }
      ],
      essays: [
        {
          id: 1,
          unit: '第三单元',
          type: '单元作文',
          category: '全命题',
          title: '我的植物朋友',
          time: '2025.09.30 14:59',
          score: null
        },
        {
          id: 2,
          unit: '第二单元',
          type: '单元作文',
          category: '全命题',
          title: '我的植物朋友',
          time: '2025.09.08 11:22',
          score: 28
        },
        {
          id: 3,
          unit: '第一单元',
          type: '单元作文',
          category: '全命题',
          title: '我的植物朋友',
          time: '2025.09.08 11:22',
          score: 28
        }
      ]
    }
  },
  computed: {
    selectedChild() {
      return this.children.find(child => child.id === this.selectedChildId)
    }
  },
  methods: {
    selectChild(childId) {
      this.selectedChildId = childId
    },
    addChild() {
      console.log('添加孩子')
    },
    uploadEssay(essayId) {
      console.log('上传作文:', essayId)
    },
    switchTab(tab) {
      this.currentTab = tab
      console.log('切换到:', tab)
    }
  }
}
</script>

<style scoped>
.home-page {
  width: 375px;
  height: 844px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  height: 40px;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.status-left {
  display: flex;
  align-items: center;
}

.signal-bars {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.bar {
  width: 2.55px;
  background: #000000;
}

.bar:nth-child(1) {
  height: 3.4px;
}

.bar:nth-child(2) {
  height: 5.53px;
}

.bar:nth-child(3) {
  height: 8.51px;
}

.bar:nth-child(4) {
  height: 10.21px;
}

.status-right {
  display: flex;
  align-items: center;
}

.battery-indicator {
  width: 20.28px;
  height: 10.06px;
  border: 1px solid #000000;
  border-radius: 2px;
  position: relative;
  opacity: 0.35;
}

.battery-level {
  width: 17.87px;
  height: 7.66px;
  background: #000000;
  position: absolute;
  top: 1.2px;
  left: 1.21px;
}

/* 用户信息区域 */
.user-header {
  height: 52px;
  display: flex;
  align-items: center;
  padding: 8px 24px;
  border-bottom: 1px solid #FFFFFF;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  border: 1px solid #636AE8;
  background: #CED0F8;
  overflow: hidden;
  margin-right: 14px;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-name {
  font-weight: 700;
  font-size: 18px;
  line-height: 28px;
  color: #323842;
}

/* 孩子选择区域 */
.children-section {
  background: #F8F9FA;
  padding: 18px 12px;
  display: flex;
  gap: 20px;
  align-items: center;
}

.child-avatar {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.child-avatar img {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  border: 2px solid transparent;
  object-fit: cover;
}

.child-avatar:nth-child(1) img {
  border-color: #7F55E0;
  background: #D8CBF5;
}

.child-avatar:nth-child(2) img {
  border-color: #22CCB2;
  background: #BAF3EB;
}

.child-avatar:nth-child(3) img {
  border-color: #E8618C;
  background: #F8CEDB;
}

.child-avatar.active img {
  border-color: #7F55E0;
}

.child-name {
  font-size: 14px;
  line-height: 22px;
  color: #323842;
  margin-top: 8px;
  text-align: center;
}

.add-child-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.add-icon-container {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  border: 1px dashed #7F55E0;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 0px 1px 0px rgba(23, 26, 31, 0.07), 0px 0px 2px 0px rgba(23, 26, 31, 0.12);
}

.add-icon-container img {
  width: 24px;
  height: 24px;
}

/* 当前孩子信息卡片 */
.child-info-card {
  background: #FFFFFF;
  padding: 18px 24px;
  display: flex;
  align-items: center;
  gap: 14px;
  position: relative;
}

.child-avatar-large {
  width: 38px;
  height: 38px;
  border-radius: 19px;
  border: 1px solid #7F55E0;
  overflow: hidden;
  flex-shrink: 0;
}

.child-avatar-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.child-details {
  flex: 1;
}

.child-name-large {
  font-weight: 700;
  font-size: 12px;
  line-height: 20px;
  color: #171A1F;
  margin-bottom: 2px;
}

.child-meta {
  display: flex;
  gap: 10px;
  margin-bottom: 18px;
}

.birth-date, .age {
  font-size: 11px;
  line-height: 18px;
  color: #9095A0;
}

.school-info {
  display: flex;
  gap: 8px;
  align-items: center;
}

.school-name, .class-name, .student-number {
  font-size: 14px;
  line-height: 22px;
  color: #171A1F;
}

.wechat-icon {
  position: absolute;
  top: 20px;
  right: 24px;
  width: 16px;
  height: 16px;
}

.wechat-icon img {
  width: 100%;
  height: 100%;
}

/* 学年信息 */
.academic-year {
  background: #F8F9FA;
  padding: 8px 18px;
  font-weight: 700;
  font-size: 12px;
  line-height: 20px;
  color: #171A1F;
}

/* 作文列表 */
.essay-list {
  background: #FFFFFF;
}

.essay-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 11px 17px;
  border-bottom: 20px solid #F8F9FA;
  position: relative;
}

.essay-content {
  flex: 1;
}

.essay-tags {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.unit-tag, .type-tag, .category-tag {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
}

.essay-title {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  margin-bottom: 17px;
}

.essay-time {
  font-size: 8px;
  line-height: 14px;
  color: #9095A0;
}

.essay-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.essay-score {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.score-number {
  font-size: 24px;
  line-height: 36px;
  color: #DE3B40;
}

.score-unit {
  font-size: 12px;
  line-height: 20px;
  color: #DE3B40;
}

.upload-btn {
  background: #636AE8;
  border: none;
  border-radius: 4px;
  padding: 1px 8px;
  font-size: 11px;
  line-height: 18px;
  color: #FFFFFF;
  cursor: pointer;
}

.upload-btn:hover {
  background: #5258d6;
}

.arrow-icon {
  width: 16px;
  height: 16px;
}

.arrow-icon img {
  width: 100%;
  height: 100%;
}

/* 底线 */
.bottom-line {
  text-align: center;
  padding: 14px 0;
  font-size: 8px;
  line-height: 14px;
  color: #9095A0;
  background: #F8F9FA;
}

/* 底部导航栏 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 375px;
  height: 48px;
  background: transparent;
  display: flex;
  padding: 0 6px;
  box-sizing: border-box;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
}

.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

.tab-text {
  font-size: 10px;
  line-height: 16px;
  color: #424955;
}

.tab-item.active .tab-text {
  font-weight: 700;
  color: #4850E4;
}

.tab-indicator {
  position: absolute;
  bottom: 2.8px;
  width: 12px;
  height: 12px;
  background: #DE3B40;
  border: 2px solid #FFFFFF;
  border-radius: 5px;
}
</style>
